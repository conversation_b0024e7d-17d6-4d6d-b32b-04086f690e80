import { ErrorCode } from '@drata/enums';
import { BadRequestException } from '@nestjs/common';
import { addLeadingSlash } from '@nestjs/common/utils/shared.utils';
import { insertTextBeforeFileExtension } from 'commons/helpers/string.helper';
import { FileTypes } from 'commons/maps/file-extensions-and-mime-types-validation.map';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { SymlinkType } from 'commons/types/symlink.type';
import config from 'config';
import { format } from 'date-fns';
import { isEmpty, replace } from 'lodash';
import moment from 'moment';
import path from 'path';

type FileNameInfo = {
    fileName: string;
    fileExtension: string;
};

type ManifestEntry = {
    originalPath: string;
    abbreviatedPath: string;
    abbreviatedFilename: string;
};

function validateOriginalFileNameOrFail(originalFileName: string): void {
    if (originalFileName.length >= config.get('db.varcharLength')) {
        throw new BadRequestException(ErrorCode[ErrorCode.INVALID_FILE_NAME]);
    }
}

function getFileNameFromPath(file: string): FileNameInfo {
    const fileName: string = path.basename(file);
    const fileExtension = path.extname(file);

    return {
        fileName,
        fileExtension,
    };
}

function sanitizeFileName(name: string): string {
    return replace(name, /(?:\.(?![^.]+$)|[^\w.]+)/g, '-');
}

/**
 * Based on local dev or on a server
 * the system needs to know to load the file
 * on the "src" or "dist" path.
 *
 * @param filePath The file path after "src" or "dist"
 */
function pathResolve(filePath: string): string {
    const prefix = config.get('api.pathPrefix');

    if (isEmpty(filePath) || isEmpty(prefix)) {
        return '';
    }

    return path.resolve(`${prefix}${addLeadingSlash(filePath)}`);
}

function makeFileNamesUniqueByEnumeration<T extends FileBufferType>(files: T[]): T[] {
    const fileNameMapAndCount = new Map<string, number>();

    return files.map(item => {
        const originalFileName = item.filename;
        const itemNameAlreadyInList = fileNameMapAndCount.get(originalFileName);
        if (itemNameAlreadyInList) {
            item.filename = insertTextBeforeFileExtension(
                originalFileName,
                `_${itemNameAlreadyInList}`,
            );
        }
        const updatedNameCount = itemNameAlreadyInList ? itemNameAlreadyInList + 1 : 1;
        fileNameMapAndCount.set(originalFileName, updatedNameCount);

        return item;
    });
}

function getMimeTypeFromFileTypes(fileName: string): string | null {
    const extension = fileName.split('.').pop()?.toLowerCase();

    if (!extension) return null;

    for (const [, fileTypes] of FileTypes.entries()) {
        for (const fileType of fileTypes) {
            if (fileType.extension.toLowerCase() === `.${extension}`) {
                return fileType.mimeTypes[0] || null;
            }
        }
    }

    return null;
}

function sortByDCFNumber(a: string, b: string) {
    const extractDCFNumber = (filename: string): number | null => {
        const match = filename.match(/DCF(\d+)-/);
        return match ? parseInt(match[1], 10) : null;
    };

    const dcfA = extractDCFNumber(a);
    const dcfB = extractDCFNumber(b);

    // If both have DCF numbers, sort by DCF number
    if (dcfA !== null && dcfB !== null) {
        return dcfA - dcfB;
    }

    // If only A has DCF number, A comes first
    if (dcfA !== null && dcfB === null) {
        return -1;
    }

    // If only B has DCF number, B comes first
    if (dcfA === null && dcfB !== null) {
        return 1;
    }

    // If neither has DCF number, sort alphabetically
    return a.localeCompare(b);
}

/**
 * Generates an HTML manifest file that maps abbreviated filenames to their original paths.
 * The HTML includes clickable links that show the original routes.
 */
function generateHtmlManifest(
    manifestEntries: ManifestEntry[],
    options?: { title?: string; pageSize?: number },
): string {
    const title = options?.title || 'Drata Evidence Manifest';
    const pageSize = options?.pageSize || 50; // Default to 50 items per page

    if (manifestEntries.length === 0) {
        return `<!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${title}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { color: #333; }
                        .no-files { color: #666; font-style: italic; }
                    </style>
                </head>
                <body>
                    <h1>Evidence Manifest</h1>
                    <p class="no-files">No abbreviated files found.</p>
                </body>
                </html>`;
    }

    // Sort files with symlinks by DCF number if applies
    const hasDcfNumber = manifestEntries.some(paths => /DCF(\d+)-/.test(paths.originalPath));

    if (hasDcfNumber) {
        manifestEntries.sort((a, b) => sortByDCFNumber(a.originalPath, b.originalPath));
    } else {
        // sort files alphabetically
        manifestEntries
            .map(entry => ({ ...entry, filename: entry.originalPath }))
            .sort((a, b) => {
                return a.filename.localeCompare(b.filename);
            });
    }

    // Generate all table rows as JSON data for JavaScript pagination
    const tableRowsData = manifestEntries.map(
        ({ originalPath, abbreviatedPath, abbreviatedFilename }) => {
            const controlName = originalPath.split('/').shift();
            return {
                controlName,
                originalPath: originalPath.replace(controlName + '/', ''),
                abbreviatedPath,
                abbreviatedFilename,
            };
        },
    );

    return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        line-height: 1.6;
                    }
                    h1 {
                        color: #333;
                        border-bottom: 2px solid #007acc;
                        padding-bottom: 10px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                    }
                    th, td {
                        padding: 12px;
                        text-align: left;
                        border-bottom: 1px solid #ddd;
                    }
                    th {
                        background-color: #f8f9fa;
                        font-weight: bold;
                        color: #333;
                    }
                    tr:hover {
                        background-color: #f5f5f5;
                    }
                    code {
                        background-color: #f1f1f1;
                        padding: 2px 4px;
                        border-radius: 3px;
                        font-family: 'Courier New', monospace;
                    }
                    a {
                        color: #007acc;
                        text-decoration: none;
                    }
                    a:hover {
                        text-decoration: underline;
                    }
                    .description {
                        color: #666;
                        margin-bottom: 20px;
                    }
                    .pagination-controls {
                        margin: 20px 0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        flex-wrap: wrap;
                        gap: 10px;
                    }
                    .pagination-info {
                        color: #666;
                        font-size: 14px;
                    }
                    .pagination-buttons {
                        display: flex;
                        gap: 5px;
                        align-items: center;
                    }
                    .pagination-button {
                        padding: 8px 12px;
                        border: 1px solid #ddd;
                        background-color: #fff;
                        color: #333;
                        cursor: pointer;
                        border-radius: 4px;
                        text-decoration: none;
                        font-size: 14px;
                    }
                    .pagination-button:hover:not(.disabled) {
                        background-color: #f5f5f5;
                        border-color: #007acc;
                    }
                    .pagination-button.active {
                        background-color: #007acc;
                        color: white;
                        border-color: #007acc;
                    }
                    .pagination-button.disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                    .page-size-selector {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    .page-size-selector select {
                        padding: 5px 8px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        font-size: 14px;
                    }
                </style>
            </head>
            <body>
                <h1>${title}</h1>
                <p class="description">
                    This manifest shows the mapping between abbreviated filenames and their original paths.
                </p>

                <div class="pagination-controls">
                    <div class="pagination-info">
                        <span id="pagination-info">Showing 0 - 0 of 0 entries</span>
                    </div>
                    <div class="page-size-selector">
                        <label for="page-size">Show:</label>
                        <select id="page-size">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50" selected>50</option>
                            <option value="100">100</option>
                            <option value="all">All</option>
                        </select>
                        <span>entries</span>
                    </div>
                </div>

                <table id="evidence-table">
                    <thead>
                        <tr>
                            <th>Evidence</th>
                            <th>Original Path</th>
                            <th>Abbreviated Filename (/Evidence)</th>
                        </tr>
                    </thead>
                    <tbody id="table-body">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>

                <div class="pagination-controls">
                    <div class="pagination-buttons" id="pagination-buttons">
                        <!-- Pagination buttons will be populated by JavaScript -->
                    </div>
                </div>

                <script>
                    // Table data
                    const tableData = ${JSON.stringify(tableRowsData)};
                    let currentPage = 1;
                    let pageSize = ${pageSize};

                    function renderTable() {
                        const tbody = document.getElementById('table-body');
                        const startIndex = (currentPage - 1) * pageSize;
                        const endIndex = pageSize === 'all' ? tableData.length : startIndex + pageSize;
                        const pageData = pageSize === 'all' ? tableData : tableData.slice(startIndex, endIndex);

                        tbody.innerHTML = pageData.map(row =>
                            \`<tr>
                                <td>\${row.controlName}</td>
                                <td><code>\${row.originalPath}</code></td>
                                <td>
                                    <a href="\${row.abbreviatedPath}"
                                       title="ControlEvidence/Evidence/\${row.abbreviatedFilename}"
                                       download>\${row.abbreviatedFilename}</a>
                                </td>
                            </tr>\`
                        ).join('');

                        updatePaginationInfo();
                        updatePaginationButtons();
                    }

                    function updatePaginationInfo() {
                        const totalItems = tableData.length;
                        const startIndex = pageSize === 'all' ? 1 : (currentPage - 1) * pageSize + 1;
                        const endIndex = pageSize === 'all' ? totalItems : Math.min(currentPage * pageSize, totalItems);

                        document.getElementById('pagination-info').textContent =
                            \`Showing \${startIndex} - \${endIndex} of \${totalItems} entries\`;
                    }

                    function updatePaginationButtons() {
                        const totalPages = pageSize === 'all' ? 1 : Math.ceil(tableData.length / pageSize);
                        const buttonsContainer = document.getElementById('pagination-buttons');

                        if (pageSize === 'all' || totalPages <= 1) {
                            buttonsContainer.innerHTML = '';
                            return;
                        }

                        let buttonsHTML = '';

                        // Previous button
                        buttonsHTML += \`<button class="pagination-button \${currentPage === 1 ? 'disabled' : ''}"
                                        onclick="changePage(\${currentPage - 1})" \${currentPage === 1 ? 'disabled' : ''}>
                                        Previous
                                      </button>\`;

                        // Page numbers
                        const maxVisiblePages = 5;
                        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                        if (endPage - startPage + 1 < maxVisiblePages) {
                            startPage = Math.max(1, endPage - maxVisiblePages + 1);
                        }

                        if (startPage > 1) {
                            buttonsHTML += \`<button class="pagination-button" onclick="changePage(1)">1</button>\`;
                            if (startPage > 2) {
                                buttonsHTML += \`<span class="pagination-button disabled">...</span>\`;
                            }
                        }

                        for (let i = startPage; i <= endPage; i++) {
                            buttonsHTML += \`<button class="pagination-button \${i === currentPage ? 'active' : ''}"
                                            onclick="changePage(\${i})">\${i}</button>\`;
                        }

                        if (endPage < totalPages) {
                            if (endPage < totalPages - 1) {
                                buttonsHTML += \`<span class="pagination-button disabled">...</span>\`;
                            }
                            buttonsHTML += \`<button class="pagination-button" onclick="changePage(\${totalPages})">\${totalPages}</button>\`;
                        }

                        // Next button
                        buttonsHTML += \`<button class="pagination-button \${currentPage === totalPages ? 'disabled' : ''}"
                                        onclick="changePage(\${currentPage + 1})" \${currentPage === totalPages ? 'disabled' : ''}>
                                        Next
                                      </button>\`;

                        buttonsContainer.innerHTML = buttonsHTML;
                    }

                    function changePage(page) {
                        const totalPages = Math.ceil(tableData.length / pageSize);
                        if (page >= 1 && page <= totalPages && page !== currentPage) {
                            currentPage = page;
                            renderTable();
                        }
                    }

                    function changePageSize(newPageSize) {
                        pageSize = newPageSize === 'all' ? 'all' : parseInt(newPageSize);
                        currentPage = 1;
                        renderTable();
                    }

                    // Event listeners
                    document.getElementById('page-size').addEventListener('change', function(e) {
                        changePageSize(e.target.value);
                    });

                    // Initial render
                    renderTable();
                </script>
            </body>
            </html>`;
}

function abbreviateFilenames(
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
    symlinks: SymlinkType[],
) {
    const manifest: ManifestEntry[] = [];
    const controlEvidencePath = 'ControlEvidence/Evidence/';

    // Sort files by DCF number if applies
    const hasDcfNumber = evidenceFilesJson.some(file => /DCF(\d+)-/.test(file.filename));

    if (hasDcfNumber) {
        evidenceFilesJson.sort((a, b) => sortByDCFNumber(a.filename, b.filename));
    } else {
        // sort files alphabetically (for Custom Frameworks)
        evidenceFilesJson.sort((a, b) => {
            return a.filename.localeCompare(b.filename);
        });
    }

    // Abbreviate filenames
    evidenceFilesJson
        .filter(({ filename }) => filename.includes(controlEvidencePath))
        .forEach((file, index) => {
            const dirs = file.filename.split('/');
            const fileName = dirs.pop() as unknown as string;
            const extension = path.extname(fileName);
            const abbreviatedFileName = index + 1;
            file.filename = `${controlEvidencePath}${abbreviatedFileName}${extension}`; // updates by reference

            manifest.push({
                originalPath: `${dirs.join('/').replace(controlEvidencePath, '')}/${fileName}`,
                abbreviatedPath: file.filename,
                abbreviatedFilename: `${abbreviatedFileName}${extension}`,
            });
        });

    // Add abbreviated Evidence symlinks to manifest
    symlinks
        .filter(symlink => symlink.link.includes(controlEvidencePath))
        .forEach(symlink => {
            const originalPathManifestEntry = manifest.find(paths => {
                const firstDirOfOriginalPath = paths.originalPath.split('/')[0]; // Personnel Evidence, Vendor info, etc
                const symlinkDirectoryFromControl = symlink.link.split('/')[3]; // ControlEvidence/Evidence/{controlFolder}/{evidenceSymlink}
                return symlinkDirectoryFromControl.includes(firstDirOfOriginalPath);
            });

            if (originalPathManifestEntry) {
                const controlFolder = symlink.link.replace(controlEvidencePath, '').split('/')[0];
                const originalFilePath = originalPathManifestEntry.originalPath;

                manifest.push({
                    originalPath: controlFolder + '/' + originalFilePath,
                    abbreviatedPath: originalPathManifestEntry.abbreviatedPath,
                    abbreviatedFilename: originalPathManifestEntry.abbreviatedFilename,
                });
            }
        });

    // Generate HTML manifest content
    const htmlControlEvidenceContent = generateHtmlManifest(manifest, { pageSize: 50 });

    // Add manifest.html to evidenceFilesJson
    evidenceFilesJson.push({
        filename: 'evidence_manifest.html',
        stream: Buffer.from(htmlControlEvidenceContent).toString('base64'),
    });

    // Create manifest files for each requirement
    abbreviateSymbolicLinksInRequirementFolders(evidenceFilesJson, symlinks, manifest);
}

function abbreviateSymbolicLinksInRequirementFolders(
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
    symlinks: SymlinkType[],
    manifest: ManifestEntry[],
) {
    const controlEvidencePath = 'ControlEvidence/Evidence/';
    const requirementFolders = symlinks
        .filter(symlink => !symlink.link.includes(controlEvidencePath))
        .reduce(
            (acc, symlink) => {
                const dirs = symlink.link.split('/');
                const requirementFolderPath = dirs.slice(0, 3).join('/'); // Pattern: ControlEvidence/<Framework>/<Requirement>
                acc[requirementFolderPath] = acc[requirementFolderPath] || [];
                acc[requirementFolderPath].push(
                    symlink.link.replace(requirementFolderPath + '/', ''),
                );

                return acc;
            },
            {} as Record<string, string[]>,
        );

    Object.entries(requirementFolders).forEach(([requirementFolderPath, symbolicLinks]) => {
        const requirementManifest = symbolicLinks.reduce((acc, symlink) => {
            const originalPathManifestEntries = manifest.filter(paths => {
                return paths.originalPath.includes(symlink);
            });

            if (originalPathManifestEntries.length) {
                const newEntries = originalPathManifestEntries.map(entry => ({
                    ...entry,
                    abbreviatedPath: '../../../' + entry.abbreviatedPath, // Pattern: ControlEvidence/<Framework>/<Requirement>
                }));

                acc.push(...newEntries);
            }

            return acc;
        }, [] as ManifestEntry[]);

        const requirementFolder = requirementFolderPath.split('/').pop();

        const htmlRequirementManifestContent = generateHtmlManifest(requirementManifest, {
            title: `${requirementFolder} - Evidence Manifest`,
            pageSize: 50,
        });

        evidenceFilesJson.push({
            filename: requirementFolderPath + `/${requirementFolder}-evidence_manifest.html`,
            stream: Buffer.from(htmlRequirementManifestContent).toString('base64'),
        });
    });
}

function removeDuplicatedFiles(
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
) {
    const filenames = [...new Set(evidenceFilesJson.map(file => file.filename))];
    return filenames.map(
        filename =>
            evidenceFilesJson.find(file => file.filename === filename) as {
                stream: string;
                filename: string;
            },
    );
}

/*
 * Generate a common file name for monitor test reports
 * @param type - Report type ('included' for failing resources, 'excluded' for excluded results)
 * @param testId - The test ID
 * @param testName - The test name (will be sanitized)
 * @param date - Optional date to use (defaults to current date)
 * @returns Common file name without extension
 */
function generateMonitorTestReportFileName(
    type: 'included' | 'excluded',
    testId: number,
    testName: string,
    date?: Date,
): string {
    const reportType = type === 'included' ? 'Failing-Resources' : 'Excluded-Results';
    const sanitizedTestName = sanitizeFileName(testName);
    const formattedDate = format(date || moment().toDate(), 'MMddyyyy');

    return sanitizeFileName(
        `${reportType}-For-Test-${testId}-${sanitizedTestName}-${formattedDate}`,
    );
}

export {
    abbreviateFilenames,
    generateMonitorTestReportFileName,
    getFileNameFromPath,
    getMimeTypeFromFileTypes,
    makeFileNamesUniqueByEnumeration,
    pathResolve,
    removeDuplicatedFiles,
    sanitizeFileName,
    validateOriginalFileNameOrFail,
};
